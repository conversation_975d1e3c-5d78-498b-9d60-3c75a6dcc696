import os
import numpy as np
import h5py
import pickle
import json
import traceback

# 检查路径是否存在
path = r"E:\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\MIT_rawdata_mat"
if not os.path.exists(path):
    print(f"错误: 路径 '{path}' 不存在，请检查")
    exit(1)

print(f"开始处理文件夹: {path}")
print(f"文件夹中包含 {len([f for f in os.listdir(path) if '.mat' in f])} 个.mat文件")

# 自定义JSON编码器处理NumPy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)

for f_name in os.listdir(path):
    if ".mat" in f_name:
        try:
            print(f"\n开始处理文件: {f_name}")
            base_name = f_name.split('_batchdata')[0]
            data_path = os.path.join(path, f_name)
            f = h5py.File(data_path)
            print(f"文件已打开，键列表: {list(f.keys())}")
            batch = f["batch"]
            print(f"批次键列表: {list(batch.keys())}")
            num_cells = batch["summary"].shape[0]
            print(f"单元格数量: {num_cells}")
            bat_dict = {}
            for i in range(num_cells):
                print(f"  处理单元格 {i+1}/{num_cells}")
                cl = f[batch['cycle_life'][i, 0]][:]
                policy = f[batch['policy_readable'][i, 0]][:].tobytes()[::2].decode()
                # print(policy)
                summary_IR = np.hstack(f[batch['summary'][i, 0]]['IR'][0, :].tolist())
                summary_IR_2 = f[batch['summary'][i, 0]]['IR'][:]  # 被存成了1*n数组 [[0.         0.01643758 0.01639642 ... 0.01992088 0.01983371 0.0195273 ]]
                # print(summary_IR_2)
                summary_QC = np.hstack(f[batch['summary'][i, 0]]['QCharge'][0, :].tolist())  # 已有batch = f["batch"]
                summary_QD = np.hstack(f[batch['summary'][i, 0]]['QDischarge'][0, :].tolist())
                summary_TA = np.hstack(f[batch['summary'][i, 0]]['Tavg'][0, :].tolist())
                summary_TM = np.hstack(f[batch['summary'][i, 0]]['Tmin'][0, :].tolist())
                summary_TX = np.hstack(f[batch['summary'][i, 0]]['Tmax'][0, :].tolist())
                summary_CT = np.hstack(f[batch['summary'][i, 0]]['chargetime'][0, :].tolist())
                summary_CY = np.hstack(f[batch['summary'][i, 0]]['cycle'][0, :].tolist())
                summary = {'IR': summary_IR, 'QC': summary_QC, 'QD': summary_QD, 'Tavg':
                    summary_TA, 'Tmin': summary_TM, 'Tmax': summary_TX, 'chargetime': summary_CT,
                           'cycle': summary_CY}
                cycles = f[batch['cycles'][i, 0]]
                cycle_dict = {}
                for j in range(cycles['I'].shape[0]):
                    I = np.hstack((f[cycles['I'][j, 0]][:]))
                    Qc = np.hstack((f[cycles['Qc'][j, 0]][:]))
                    Qd = np.hstack((f[cycles['Qd'][j, 0]][:]))
                    Qdlin = np.hstack((f[cycles['Qdlin'][j, 0]][:]))
                    T = np.hstack((f[cycles['T'][j, 0]][:]))
                    Tdlin = np.hstack((f[cycles['Tdlin'][j, 0]][:]))
                    V = np.hstack((f[cycles['V'][j, 0]][:]))
                    dQdV = np.hstack((f[cycles['discharge_dQdV'][j, 0]][:]))
                    t = np.hstack((f[cycles['t'][j, 0]][:]))
                    cd = {'I': I, 'Qc': Qc, 'Qd': Qd, 'Qdlin': Qdlin, 'T': T, 'Tdlin': Tdlin, 'V': V, 'dQdV': dQdV, 't': t}
                    cycle_dict[str(j)] = cd

                cell_dict = {'cycle_life': cl, 'charge_policy': policy, 'summary': summary, 'cycles': cycle_dict}
                key = 'b3c' + str(i)
                bat_dict[key] = cell_dict
                print(f"  已添加单元格 {key}, 当前键列表: {list(bat_dict.keys())}")
            
            save_name = f'{base_name}_batch.json'
            full_path = os.path.join(path, save_name)
            print(f"保存文件到: {full_path}")
            with open(full_path, 'w') as fp:
                json.dump(bat_dict, fp, indent=2, cls=NumpyEncoder)
                print(f"文件 {save_name} 保存成功!")
            
            # 关闭h5py文件
            f.close()
            
        except Exception as e:
            print(f"处理文件 {f_name} 时出错:")
            print(traceback.format_exc())
            continue

print("所有文件处理完成!")