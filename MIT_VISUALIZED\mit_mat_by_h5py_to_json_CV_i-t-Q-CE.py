import os
import numpy as np
import h5py
import pickle
import json
import traceback
import argparse
import multiprocessing as mp
from tqdm import tqdm
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, Generator, List
import time
import psutil
import gc


# 设置日志
def setup_logging(log_level: str = "INFO") -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("cv_extraction.log", encoding="utf-8"),
        ],
    )


# 内存监控工具
def get_memory_usage() -> float:
    """获取当前进程内存使用量(MB)"""
    try:
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except:
        return 0.0


def get_available_memory() -> float:
    """获取系统可用内存(MB)"""
    try:
        return psutil.virtual_memory().available / 1024 / 1024
    except:
        return 1024.0  # 默认假设1GB可用


def log_memory_usage(context: str = "") -> None:
    """记录内存使用情况"""
    current_memory = get_memory_usage()
    available_memory = get_available_memory()
    logging.debug(
        f"内存使用 {context}: 当前进程 {current_memory:.1f}MB, 系统可用 {available_memory:.1f}MB"
    )


# 自定义JSON编码器处理NumPy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)


def extract_cv_phase(
    V,
    I,
    Qc,
    t,
    cv_voltage=3.6,
    tolerance=0.001,
    max_non_cv_duration=0.25,  # 保留参数但不使用
    cv_i_max=1.0005,
):
    """
    提取恒压阶段数据 - 新逻辑
    参数:
        V: 电压数组
        I: 电流数组
        Qc: 充电容量数组
        t: 时间数组 (单位: 分钟)
        cv_voltage: 恒压阈值 (默认3.6V)
        tolerance: 电压容差 (默认±0.001V)
        max_non_cv_duration: 保留参数兼容性
        cv_i_max: CV阶段电流最大值 (默认1.0005A)

    返回:
        dict: 包含CV阶段数据的字典，其中时间(t)已转换为秒并从0开始
              如果起点超过终点，返回全零数据
    """
    if len(V) == 0 or len(I) == 0 or len(Qc) == 0 or len(t) == 0:
        logging.debug("输入数据为空")
        return None

    # 参数验证
    if cv_voltage <= 0:
        logging.warning(f"无效的恒压阈值: {cv_voltage}")
        return None
    if tolerance <= 0:
        logging.warning(f"无效的电压容差: {tolerance}")
        return None
    if cv_i_max <= 0:
        logging.warning(f"无效的CV阶段电流最大值: {cv_i_max}")
        return None

    # 步骤1: 确定恒压阶段终点
    # 1.1 找到电流为负值的第一个点
    negative_current_idx = None
    for i in range(len(I)):
        if I[i] < 0:
            negative_current_idx = i
            break

    if negative_current_idx is None:
        logging.debug("未找到负电流点，无法确定终点")
        return None

    # 1.2 从负电流点往前查找电流超过cv_i_max/25的点
    cv_end = None
    current_threshold = cv_i_max / 25

    for i in range(negative_current_idx - 1, -1, -1):  # 往前查找
        if I[i] > current_threshold:
            # 1.3 验证该点电压是否在恒压阈值附近
            if abs(V[i] - cv_voltage) <= tolerance:
                cv_end = i
                break
            # 如不满足电压条件，继续向前查找

    if cv_end is None:
        logging.debug("没有找到合适的终点")
        return None

    logging.debug(f"确定恒压终点: 索引 {cv_end}")

    # 步骤2: 确定恒压阶段起点
    # 2.1 找到第一个同时满足电压和电流条件的点作为初步起点
    cv_start = None
    for i in range(cv_end + 1):  # 从开始到终点查找
        voltage_ok = abs(V[i] - cv_voltage) <= tolerance
        current_ok = I[i] <= cv_i_max
        if voltage_ok and current_ok:
            cv_start = i
            break

    if cv_start is None:
        logging.debug("未找到满足起点条件的点")
        return None

    logging.debug(f"初步起点: 索引 {cv_start}")

    # 2.2 检查起点到终点区间内的连续不满足条件的点
    while cv_start < cv_end:
        # 查找连续2个不满足条件的相邻点
        found_consecutive_bad = False

        for i in range(cv_start, cv_end):  # 检查相邻点对
            voltage_ok_1 = abs(V[i] - cv_voltage) <= tolerance
            current_ok_1 = I[i] <= cv_i_max
            voltage_ok_2 = abs(V[i + 1] - cv_voltage) <= tolerance
            current_ok_2 = I[i + 1] <= cv_i_max

            # 检查连续两个点是否都不满足条件
            if not (voltage_ok_1 and current_ok_1) and not (
                voltage_ok_2 and current_ok_2
            ):
                # 发现连续2个不满足条件的点，更新起点
                cv_start = i + 2  # 跳到这些点之后
                logging.debug(
                    f"发现连续不满足条件的点 {i}-{i+1}，更新起点到索引 {cv_start}"
                )
                found_consecutive_bad = True
                break

        if not found_consecutive_bad:
            # 没有发现连续不满足条件的点，起点确定
            break

    # 检查起点是否超过终点
    if cv_start > cv_end:
        logging.debug("起点超过终点，返回全零数据")
        # 返回全零数据
        zero_length = 10  # 返回10个零点
        return {
            "I": np.zeros(zero_length),
            "Qc": np.zeros(zero_length),
            "V": np.zeros(zero_length),
            "t": np.zeros(zero_length),
        }

    logging.debug(f"最终恒压范围: 索引 {cv_start} 到 {cv_end}")

    # 提取恒压阶段数据
    t_cv = t[cv_start : cv_end + 1]

    # 时间处理：转换为秒并从0开始
    t_cv_seconds = (t_cv - t_cv[0]) * 60  # 从分钟转换为秒，并设置初始值为0

    cv_data = {
        "I": I[cv_start : cv_end + 1],
        "Qc": Qc[cv_start : cv_end + 1],
        "V": V[cv_start : cv_end + 1],
        "t": t_cv_seconds,  # 使用转换后的时间（秒，从0开始）
    }

    return cv_data


def calculate_optimal_batch_size(
    available_memory_mb: float, estimated_cycle_size_mb: float
) -> int:
    """
    计算最优批处理大小

    参数:
        available_memory_mb: 可用内存(MB)
        estimated_cycle_size_mb: 估计的单个循环数据大小(MB)

    返回:
        int: 最优批处理大小
    """
    # 保守估计，使用可用内存的30%
    usable_memory = available_memory_mb * 0.3

    # 确保至少处理1个循环，最多处理100个循环
    batch_size = max(
        1, min(100, int(usable_memory / max(estimated_cycle_size_mb, 0.1)))
    )

    logging.debug(
        f"计算批处理大小: 可用内存{available_memory_mb:.1f}MB, "
        f"估计循环大小{estimated_cycle_size_mb:.1f}MB, 批处理大小{batch_size}"
    )

    return batch_size


def process_cycles_in_batches(
    cycles_ref,
    h5_file,
    cv_voltage: float,
    tolerance: float,
    cv_i_max: float = 1.0005,
    batch_size: int = None,
) -> Generator[Tuple[str, Dict], None, None]:
    """
    分批处理循环数据的生成器

    参数:
        cycles_ref: HDF5中cycles的引用
        h5_file: HDF5文件对象
        cv_voltage: 恒压阈值
        tolerance: 电压容差
        batch_size: 批处理大小，None表示自动计算

    生成:
        Tuple[str, Dict]: (循环ID, 恒压数据)
    """
    total_cycles = cycles_ref["I"].shape[0]

    if batch_size is None:
        # 估计单个循环的数据大小
        try:
            # 读取第一个循环来估计大小
            sample_I = np.array(h5_file[cycles_ref["I"][0, 0]][:])
            estimated_size_mb = (
                sample_I.nbytes * 4 / 1024 / 1024
            )  # I, Qc, V, t 四个数组
            available_memory = get_available_memory()
            batch_size = calculate_optimal_batch_size(
                available_memory, estimated_size_mb
            )
        except:
            batch_size = 10  # 默认批处理大小

    logging.info(f"开始分批处理 {total_cycles} 个循环，批处理大小: {batch_size}")

    for batch_start in range(0, total_cycles, batch_size):
        batch_end = min(batch_start + batch_size, total_cycles)
        current_batch_size = batch_end - batch_start

        logging.debug(
            f"处理批次 {batch_start}-{batch_end-1} ({current_batch_size} 个循环)"
        )
        log_memory_usage(f"批次开始")

        # 处理当前批次
        batch_results = []

        for j in range(batch_start, batch_end):
            try:
                # 逐个加载循环数据
                I = np.hstack((h5_file[cycles_ref["I"][j, 0]][:]))
                Qc = np.hstack((h5_file[cycles_ref["Qc"][j, 0]][:]))
                V = np.hstack((h5_file[cycles_ref["V"][j, 0]][:]))
                t = np.hstack((h5_file[cycles_ref["t"][j, 0]][:]))

                # 提取恒压阶段数据
                cv_data = extract_cv_phase(
                    V,
                    I,
                    Qc,
                    t,
                    cv_voltage,
                    tolerance,
                    0.25,
                    cv_i_max,  # 0.25分钟 = 15秒
                )

                if cv_data is not None:
                    batch_results.append((str(j), cv_data))

                # 及时清理临时变量
                del I, Qc, V, t

            except (KeyError, IndexError, ValueError) as e:
                logging.warning(f"  跳过循环 {j}: {str(e)}")
                continue

        # 生成批次结果
        for cycle_id, cv_data in batch_results:
            yield cycle_id, cv_data

        # 批次处理完成后清理内存
        del batch_results
        gc.collect()
        log_memory_usage(f"批次结束")


def process_single_file(
    args: Tuple[str, str, str, float, float, float, int],
) -> Optional[str]:
    """
    处理单个MAT文件

    参数:
        args: (file_path, input_dir, output_dir, cv_voltage, tolerance, cv_i_max, batch_size)

    返回:
        str: 成功处理的文件名，失败返回None
    """
    file_path, input_dir, output_dir, cv_voltage, tolerance, cv_i_max, batch_size = args
    f_name = os.path.basename(file_path)

    try:
        logging.info(f"开始处理文件: {f_name}")
        base_name = f_name.split("_batchdata")[0]

        # 检查文件是否存在和可读
        if not os.path.exists(file_path):
            logging.error(f"文件不存在: {file_path}")
            return None

        try:
            with h5py.File(file_path, "r") as f:
                logging.debug(f"文件已打开，键列表: {list(f.keys())}")

                # 检查必需的键是否存在
                if "batch" not in f:
                    logging.error(f"文件 {f_name} 缺少 'batch' 键")
                    return None

                batch = f["batch"]
                logging.debug(f"批次键列表: {list(batch.keys())}")

                # 检查必需的batch键
                required_keys = ["summary", "cycles"]
                for key in required_keys:
                    if key not in batch:
                        logging.error(f"文件 {f_name} 的batch中缺少 '{key}' 键")
                        return None

                num_cells = batch["summary"].shape[0]
                logging.info(f"文件 {f_name} 包含 {num_cells} 个单元格")

                bat_dict = {}

                for i in range(num_cells):
                    logging.debug(f"  处理单元格 {i+1}/{num_cells}")

                    try:
                        # 提取summary数据
                        summary_QC = np.hstack(
                            f[batch["summary"][i, 0]]["QCharge"][0, :].tolist()
                        )
                        summary_QD = np.hstack(
                            f[batch["summary"][i, 0]]["QDischarge"][0, :].tolist()
                        )

                        # 计算CE (容量效率)
                        CE = np.divide(
                            summary_QD,
                            summary_QC,
                            out=np.zeros_like(summary_QD),
                            where=summary_QC != 0,
                        )

                        summary = {"QC": summary_QC, "QD": summary_QD, "CE": CE}

                        # 使用流式处理提取循环数据中的恒压阶段
                        cycles = f[batch["cycles"][i, 0]]
                        cv_cycles = {}

                        logging.debug(
                            f"  单元格 {i} 开始流式处理 {cycles['I'].shape[0]} 个循环"
                        )
                        log_memory_usage(f"单元格 {i} 开始")

                        # 使用生成器进行流式处理
                        for cycle_id, cv_data in process_cycles_in_batches(
                            cycles, f, cv_voltage, tolerance, cv_i_max, batch_size
                        ):
                            cv_cycles[cycle_id] = cv_data

                        log_memory_usage(f"单元格 {i} 完成")

                        cell_dict = {"summary": summary, "cv_cycles": cv_cycles}
                        key = "b3c" + str(i)
                        bat_dict[key] = cell_dict
                        logging.debug(
                            f"  已添加单元格 {key}, 恒压循环数: {len(cv_cycles)}"
                        )

                    except (KeyError, IndexError, ValueError) as e:
                        logging.warning(f"  跳过单元格 {i}: {str(e)}")
                        continue

        except (OSError, IOError) as e:
            logging.error(f"无法读取文件 {f_name}: {str(e)}")
            return None
        except h5py.HDF5Error as e:
            logging.error(f"HDF5文件格式错误 {f_name}: {str(e)}")
            return None

        if not bat_dict:
            logging.warning(f"文件 {f_name} 没有有效的数据被提取")
            return None

        # 保存文件
        save_name = f"{base_name}_CV_CE.json"
        full_path = os.path.join(output_dir, save_name)

        try:
            with open(full_path, "w", encoding="utf-8") as fp:
                json.dump(bat_dict, fp, indent=2, cls=NumpyEncoder, ensure_ascii=False)
        except (OSError, IOError) as e:
            logging.error(f"无法保存文件 {save_name}: {str(e)}")
            return None

        logging.info(f"文件 {save_name} 保存成功到: {full_path}")
        return f_name

    except Exception as e:
        logging.error(f"处理文件 {f_name} 时发生未预期错误: {str(e)}")
        logging.debug(traceback.format_exc())
        return None


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件

    参数:
        config_path: 配置文件路径

    返回:
        Dict: 配置参数字典
    """
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        logging.info(f"已加载配置文件: {config_path}")
        return config
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        return {}


def parse_arguments() -> argparse.Namespace:
    """
    解析命令行参数

    返回:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(
        description="MIT电池数据恒压阶段提取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python %(prog)s --input-dir /path/to/mat/files
  python %(prog)s --input-dir /path/to/mat/files --output-dir /path/to/output
  python %(prog)s --config config.json
  python %(prog)s --input-dir /path/to/mat/files --workers 4 --cv-voltage 3.6
        """,
    )

    parser.add_argument(
        "--input-dir", "-i", type=str, help="输入目录路径（包含MAT文件）"
    )

    parser.add_argument(
        "--output-dir", "-o", type=str, help="输出目录路径（默认与输入目录相同）"
    )

    parser.add_argument("--config", "-c", type=str, help="配置文件路径（JSON格式）")

    parser.add_argument(
        "--workers",
        "-w",
        type=int,
        default=mp.cpu_count(),
        help=f"并行进程数（默认: {mp.cpu_count()}）",
    )

    parser.add_argument(
        "--cv-voltage", type=float, default=3.6, help="恒压阶段电压阈值（默认: 3.6V）"
    )

    parser.add_argument(
        "--tolerance", type=float, default=0.001, help="电压容差（默认: 0.001V）"
    )

    parser.add_argument(
        "--cv-i-max",
        type=float,
        default=1.0005,
        help="CV阶段电流最大值（默认: 1.0005A）",
    )

    parser.add_argument(
        "--batch-size",
        type=int,
        default=None,
        help="循环数据批处理大小（默认: 自动计算）",
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别（默认: INFO）",
    )

    parser.add_argument("--no-progress", action="store_true", help="禁用进度条显示")

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 设置日志
    setup_logging(args.log_level)

    # 加载配置文件（如果提供）
    config = {}
    if args.config:
        config = load_config(args.config)

    # 合并配置：命令行参数优先级高于配置文件
    input_dir = args.input_dir or config.get("input_dir")
    output_dir = args.output_dir or config.get("output_dir") or input_dir
    workers = (
        args.workers
        if args.workers != mp.cpu_count()
        else config.get("workers", mp.cpu_count())
    )
    cv_voltage = (
        args.cv_voltage if args.cv_voltage != 3.6 else config.get("cv_voltage", 3.6)
    )
    tolerance = (
        args.tolerance if args.tolerance != 0.001 else config.get("tolerance", 0.001)
    )
    cv_i_max = (
        args.cv_i_max if args.cv_i_max != 1.0005 else config.get("cv_i_max", 1.0005)
    )
    batch_size = args.batch_size or config.get("batch_size", None)
    no_progress = args.no_progress or config.get("no_progress", False)

    # 验证必需参数
    if not input_dir:
        logging.error("必须提供输入目录路径（--input-dir 或配置文件中的 input_dir）")
        return 1

    if not os.path.exists(input_dir):
        logging.error(f"输入目录不存在: {input_dir}")
        return 1

    # 验证参数合理性
    if cv_voltage <= 0:
        logging.error(f"恒压阈值必须大于0: {cv_voltage}")
        return 1

    if tolerance <= 0:
        logging.error(f"电压容差必须大于0: {tolerance}")
        return 1

    if cv_i_max <= 0:
        logging.error(f"CV阶段电流最大值必须大于0: {cv_i_max}")
        return 1

    if workers <= 0:
        logging.error(f"进程数必须大于0: {workers}")
        return 1

    if workers > mp.cpu_count():
        logging.warning(
            f"进程数 ({workers}) 超过CPU核心数 ({mp.cpu_count()})，可能影响性能"
        )

    # 验证输出目录权限
    try:
        os.makedirs(output_dir, exist_ok=True)
        # 测试写入权限
        test_file = os.path.join(output_dir, ".test_write_permission")
        with open(test_file, "w") as f:
            f.write("test")
        os.remove(test_file)
    except (OSError, IOError) as e:
        logging.error(f"输出目录无法写入: {output_dir}, 错误: {str(e)}")
        return 1

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有MAT文件
    mat_files = []
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith(".mat"):
                mat_files.append(os.path.join(root, file))

    if not mat_files:
        logging.warning(f"在目录 {input_dir} 中未找到MAT文件")
        return 0

    logging.info(f"找到 {len(mat_files)} 个MAT文件")
    logging.info(f"输入目录: {input_dir}")
    logging.info(f"输出目录: {output_dir}")
    logging.info(f"并行进程数: {workers}")
    logging.info(f"恒压阈值: {cv_voltage}V")
    logging.info(f"电压容差: {tolerance}V")
    logging.info(f"CV电流最大值: {cv_i_max}A")
    if batch_size:
        logging.info(f"批处理大小: {batch_size}")
    else:
        logging.info("批处理大小: 自动计算")

    # 准备处理参数
    process_args = [
        (file_path, input_dir, output_dir, cv_voltage, tolerance, cv_i_max, batch_size)
        for file_path in mat_files
    ]

    # 开始处理
    start_time = time.time()
    successful_files = []

    if workers == 1:
        # 单进程处理
        iterator = process_args
        if not no_progress:
            try:
                iterator = tqdm(process_args, desc="处理文件", unit="文件")
            except ImportError:
                logging.warning("tqdm未安装，无法显示进度条")

        for args_tuple in iterator:
            result = process_single_file(args_tuple)
            if result:
                successful_files.append(result)
    else:
        # 多进程处理
        with mp.Pool(workers) as pool:
            if not no_progress:
                try:
                    # 使用tqdm显示进度
                    results = list(
                        tqdm(
                            pool.imap(process_single_file, process_args),
                            total=len(process_args),
                            desc="处理文件",
                            unit="文件",
                        )
                    )
                except ImportError:
                    logging.warning("tqdm未安装，无法显示进度条")
                    results = pool.map(process_single_file, process_args)
            else:
                results = pool.map(process_single_file, process_args)

            successful_files = [r for r in results if r is not None]

    # 处理完成统计
    end_time = time.time()
    processing_time = end_time - start_time

    logging.info(f"处理完成!")
    logging.info(f"总文件数: {len(mat_files)}")
    logging.info(f"成功处理: {len(successful_files)}")
    logging.info(f"失败文件: {len(mat_files) - len(successful_files)}")
    logging.info(f"总耗时: {processing_time:.2f}秒")

    if len(successful_files) < len(mat_files):
        logging.warning("部分文件处理失败，请检查日志文件 cv_extraction.log")

    return 0


if __name__ == "__main__":
    exit(main())
