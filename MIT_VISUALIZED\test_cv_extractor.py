#!/usr/bin/env python3
"""
MIT电池数据恒压阶段提取工具测试脚本
"""

import os
import sys
import tempfile
import json
import numpy as np
import h5py
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_mat_file(file_path: str):
    """创建一个测试用的MAT文件"""
    print(f"创建测试文件: {file_path}")
    
    with h5py.File(file_path, 'w') as f:
        # 创建batch组
        batch = f.create_group('batch')
        
        # 创建summary数据 (1个电池单元)
        summary_group = f.create_group('summary_data')
        
        # 模拟充电和放电容量数据
        qcharge_data = np.array([1.0, 1.1, 1.05, 0.95, 0.9])  # 5个循环的充电容量
        qdischarge_data = np.array([0.95, 1.0, 0.98, 0.88, 0.82])  # 5个循环的放电容量
        
        qcharge_ref = summary_group.create_dataset('QCharge', data=qcharge_data.reshape(1, -1))
        qdischarge_ref = summary_group.create_dataset('QDischarge', data=qdischarge_data.reshape(1, -1))
        
        # 创建summary引用数组
        summary_refs = np.array([[summary_group.ref]], dtype=h5py.special_dtype(ref=h5py.Reference))
        batch.create_dataset('summary', data=summary_refs)
        
        # 创建cycles数据
        cycles_group = f.create_group('cycles_data')
        
        # 模拟3个循环的数据
        cycle_refs = []
        for cycle_idx in range(3):
            cycle_group = cycles_group.create_group(f'cycle_{cycle_idx}')
            
            # 模拟一个完整的充电过程，包含恒压阶段
            time_points = 100
            t = np.linspace(0, 3600, time_points)  # 1小时的数据
            
            # 电压：前70%是恒流阶段(电压上升)，后30%是恒压阶段(3.6V)
            V = np.concatenate([
                np.linspace(3.0, 3.6, int(time_points * 0.7)),  # 恒流阶段
                np.full(int(time_points * 0.3), 3.6)  # 恒压阶段
            ])
            
            # 电流：恒流阶段为1A，恒压阶段逐渐减小
            I = np.concatenate([
                np.full(int(time_points * 0.7), 1.0),  # 恒流阶段
                np.linspace(1.0, 0.1, int(time_points * 0.3))  # 恒压阶段
            ])
            
            # 充电容量：单调递增
            Qc = np.cumsum(I * (t[1] - t[0]) / 3600)  # 转换为Ah
            
            # 创建数据集
            I_ref = cycle_group.create_dataset('I', data=I.reshape(-1, 1))
            V_ref = cycle_group.create_dataset('V', data=V.reshape(-1, 1))
            Qc_ref = cycle_group.create_dataset('Qc', data=Qc.reshape(-1, 1))
            t_ref = cycle_group.create_dataset('t', data=t.reshape(-1, 1))
            
            # 创建引用数组
            cycle_data = np.array([
                [I_ref.ref],
                [V_ref.ref], 
                [Qc_ref.ref],
                [t_ref.ref]
            ], dtype=h5py.special_dtype(ref=h5py.Reference))
            
            cycle_refs.append(cycle_data)
        
        # 将所有循环数据组合
        all_cycles = np.array(cycle_refs, dtype=h5py.special_dtype(ref=h5py.Reference))
        cycles_refs = np.array([[all_cycles]], dtype=h5py.special_dtype(ref=h5py.Reference))
        batch.create_dataset('cycles', data=cycles_refs)
    
    print(f"测试文件创建完成: {file_path}")

def test_cv_extractor():
    """测试CV提取器"""
    print("开始测试MIT电池数据恒压阶段提取工具...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建测试MAT文件
        test_file = os.path.join(temp_dir, "test_batchdata.mat")
        create_test_mat_file(test_file)
        
        # 创建输出目录
        output_dir = os.path.join(temp_dir, "output")
        os.makedirs(output_dir, exist_ok=True)
        
        # 导入并测试CV提取器
        try:
            from mit_mat_by_h5py_to_json_CV_i_t_Q_CE import process_single_file
            
            # 测试单文件处理
            args = (test_file, temp_dir, output_dir, 3.6, 0.001)
            result = process_single_file(args)
            
            if result:
                print(f"✓ 文件处理成功: {result}")
                
                # 检查输出文件
                output_file = os.path.join(output_dir, "test_CV_CE.json")
                if os.path.exists(output_file):
                    print(f"✓ 输出文件已创建: {output_file}")
                    
                    # 验证输出内容
                    with open(output_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    print(f"✓ JSON文件加载成功")
                    print(f"  - 电池单元数: {len(data)}")
                    
                    for cell_id, cell_data in data.items():
                        print(f"  - 单元格 {cell_id}:")
                        print(f"    - 摘要数据: QC={len(cell_data['summary']['QC'])}, QD={len(cell_data['summary']['QD'])}, CE={len(cell_data['summary']['CE'])}")
                        print(f"    - 恒压循环数: {len(cell_data['cv_cycles'])}")
                        
                        # 检查恒压数据
                        for cycle_id, cv_data in cell_data['cv_cycles'].items():
                            print(f"      - 循环 {cycle_id}: I={len(cv_data['I'])}, V={len(cv_data['V'])}, Qc={len(cv_data['Qc'])}, t={len(cv_data['t'])}")
                    
                    print("✓ 所有测试通过!")
                    return True
                else:
                    print(f"✗ 输出文件未找到: {output_file}")
                    return False
            else:
                print("✗ 文件处理失败")
                return False
                
        except ImportError as e:
            print(f"✗ 导入模块失败: {e}")
            return False
        except Exception as e:
            print(f"✗ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_cv_extractor()
    sys.exit(0 if success else 1)
