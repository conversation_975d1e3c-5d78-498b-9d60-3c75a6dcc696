#!/usr/bin/env python3
"""
流式处理性能测试脚本
用于比较优化前后的内存使用和处理速度
"""

import os
import time
import psutil
import subprocess
import sys
from pathlib import Path

def get_memory_usage():
    """获取当前进程内存使用量(MB)"""
    try:
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except:
        return 0.0

def monitor_process(cmd, description):
    """监控进程的内存使用"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    start_time = time.time()
    start_memory = get_memory_usage()
    max_memory = start_memory
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 监控内存使用
        psutil_process = psutil.Process(process.pid)
        
        while process.poll() is None:
            try:
                memory_usage = psutil_process.memory_info().rss / 1024 / 1024
                max_memory = max(max_memory, memory_usage)
                time.sleep(0.5)  # 每0.5秒检查一次
            except psutil.NoSuchProcess:
                break
        
        # 等待进程完成
        stdout, stderr = process.communicate()
        end_time = time.time()
        
        # 输出结果
        duration = end_time - start_time
        print(f"\n处理完成:")
        print(f"  耗时: {duration:.2f} 秒")
        print(f"  峰值内存: {max_memory:.1f} MB")
        print(f"  返回码: {process.returncode}")
        
        if process.returncode != 0:
            print(f"  错误输出: {stderr}")
        else:
            print("  处理成功!")
            
        return {
            'duration': duration,
            'max_memory': max_memory,
            'success': process.returncode == 0
        }
        
    except Exception as e:
        print(f"监控过程出错: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("流式处理性能测试")
    print("=" * 60)
    
    # 检查是否有测试数据
    test_data_dir = input("请输入包含MAT文件的测试目录路径: ").strip()
    if not os.path.exists(test_data_dir):
        print(f"错误: 目录不存在 {test_data_dir}")
        return
    
    # 检查是否有MAT文件
    mat_files = [f for f in os.listdir(test_data_dir) if f.endswith('.mat')]
    if not mat_files:
        print(f"错误: 目录中没有找到MAT文件 {test_data_dir}")
        return
    
    print(f"找到 {len(mat_files)} 个MAT文件")
    
    # 创建输出目录
    output_dir = os.path.join(test_data_dir, "streaming_test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    script_path = "MIT_VISUALIZED/mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py"
    
    # 测试不同的批处理大小
    batch_sizes = [None, 5, 10, 20, 50]
    results = {}
    
    for batch_size in batch_sizes:
        batch_desc = "自动计算" if batch_size is None else str(batch_size)
        
        cmd = [
            sys.executable, script_path,
            "--input-dir", test_data_dir,
            "--output-dir", output_dir,
            "--workers", "1",  # 单进程测试以准确测量内存
            "--log-level", "INFO"
        ]
        
        if batch_size is not None:
            cmd.extend(["--batch-size", str(batch_size)])
        
        result = monitor_process(cmd, f"批处理大小: {batch_desc}")
        if result:
            results[batch_desc] = result
    
    # 输出比较结果
    print(f"\n{'='*60}")
    print("性能比较结果")
    print(f"{'='*60}")
    print(f"{'批处理大小':<12} {'耗时(秒)':<10} {'峰值内存(MB)':<15} {'状态'}")
    print("-" * 50)
    
    for batch_desc, result in results.items():
        status = "成功" if result['success'] else "失败"
        print(f"{batch_desc:<12} {result['duration']:<10.2f} {result['max_memory']:<15.1f} {status}")
    
    # 给出建议
    if len(results) > 1:
        print(f"\n{'='*60}")
        print("优化建议")
        print(f"{'='*60}")
        
        # 找到内存使用最少的配置
        min_memory_config = min(results.items(), key=lambda x: x[1]['max_memory'])
        print(f"内存使用最少: {min_memory_config[0]} ({min_memory_config[1]['max_memory']:.1f} MB)")
        
        # 找到速度最快的配置
        fastest_config = min(results.items(), key=lambda x: x[1]['duration'])
        print(f"处理速度最快: {fastest_config[0]} ({fastest_config[1]['duration']:.2f} 秒)")

if __name__ == "__main__":
    main()