#!/usr/bin/env python3
"""
简化的流式处理测试脚本
直接测试现有MAT文件的处理效果
"""

import os
import sys
import time
import subprocess
import json


def run_test(description, cmd_args, test_dir):
    """运行单个测试"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"{'='*60}")

    # 构建完整命令
    script_path = "MIT_VISUALIZED/mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py"
    cmd = [sys.executable, script_path] + cmd_args

    print(f"命令: {' '.join(cmd)}")

    start_time = time.time()

    try:
        # 运行命令
        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=300  # 5分钟超时
        )

        end_time = time.time()
        duration = end_time - start_time

        print(f"\n结果:")
        print(f"  耗时: {duration:.2f} 秒")
        print(f"  返回码: {result.returncode}")

        if result.returncode == 0:
            print("  ✅ 处理成功!")

            # 检查输出文件
            output_files = [
                f for f in os.listdir(test_dir) if f.endswith("_CV_CE.json")
            ]
            print(f"  生成文件数: {len(output_files)}")

            # 显示部分输出日志
            if result.stdout:
                stdout_lines = result.stdout.strip().split("\n")
                print(f"  输出日志 (最后10行):")
                for line in stdout_lines[-10:]:
                    if line.strip():
                        print(f"    {line}")
        else:
            print("  ❌ 处理失败!")
            if result.stderr:
                print(f"  错误信息: {result.stderr}")

        return {
            "success": result.returncode == 0,
            "duration": duration,
            "output_files": (
                len([f for f in os.listdir(test_dir) if f.endswith("_CV_CE.json")])
                if result.returncode == 0
                else 0
            ),
        }

    except subprocess.TimeoutExpired:
        print("  ⏰ 测试超时 (5分钟)")
        return {"success": False, "duration": 300, "output_files": 0}
    except Exception as e:
        print(f"  ❌ 测试异常: {str(e)}")
        return {"success": False, "duration": 0, "output_files": 0}


def main():
    """主测试函数"""
    print("🚀 流式处理优化效果测试")
    print("=" * 60)

    # 从配置文件读取数据目录
    config_file = "MIT_VISUALIZED/config_example.json"

    try:
        with open(config_file, "r", encoding="utf-8") as f:
            config = json.load(f)

        input_dir = config.get("input_dir")
        if not input_dir or not os.path.exists(input_dir):
            print("❌ 配置文件中的输入目录不存在，请手动输入:")
            input_dir = input("请输入MAT文件目录路径: ").strip()

            if not os.path.exists(input_dir):
                print(f"❌ 目录不存在: {input_dir}")
                return

    except Exception as e:
        print(f"❌ 无法读取配置文件: {str(e)}")
        input_dir = input("请输入MAT文件目录路径: ").strip()

        if not os.path.exists(input_dir):
            print(f"❌ 目录不存在: {input_dir}")
            return

    # 检查MAT文件
    mat_files = [f for f in os.listdir(input_dir) if f.endswith(".mat")]
    if not mat_files:
        print(f"❌ 目录中没有找到MAT文件: {input_dir}")
        return

    print(f"✅ 找到 {len(mat_files)} 个MAT文件")

    # 创建测试输出目录
    test_output_dir = os.path.join(input_dir, "streaming_test_output")
    os.makedirs(test_output_dir, exist_ok=True)

    # 清理之前的测试文件
    for f in os.listdir(test_output_dir):
        if f.endswith("_CV_CE.json"):
            os.remove(os.path.join(test_output_dir, f))

    print(f"📁 测试输出目录: {test_output_dir}")

    # 测试配置
    tests = [
        {
            "name": "自动批处理大小",
            "args": [
                "--input-dir",
                input_dir,
                "--output-dir",
                test_output_dir,
                "--workers",
                "1",
                "--log-level",
                "INFO",
            ],
        },
        {
            "name": "小批处理 (batch-size=5)",
            "args": [
                "--input-dir",
                input_dir,
                "--output-dir",
                test_output_dir,
                "--workers",
                "1",
                "--batch-size",
                "5",
                "--log-level",
                "INFO",
            ],
        },
        {
            "name": "中等批处理 (batch-size=20)",
            "args": [
                "--input-dir",
                input_dir,
                "--output-dir",
                test_output_dir,
                "--workers",
                "1",
                "--batch-size",
                "20",
                "--log-level",
                "INFO",
            ],
        },
    ]

    results = {}

    # 运行测试
    for test in tests:
        # 清理输出目录
        for f in os.listdir(test_output_dir):
            if f.endswith("_CV_CE.json"):
                os.remove(os.path.join(test_output_dir, f))

        result = run_test(test["name"], test["args"], test_output_dir)
        results[test["name"]] = result

    # 输出测试总结
    print(f"\n{'='*60}")
    print("🎯 测试结果总结")
    print(f"{'='*60}")
    print(f"{'测试配置':<25} {'状态':<8} {'耗时(秒)':<10} {'输出文件'}")
    print("-" * 55)

    for test_name, result in results.items():
        status = "✅ 成功" if result["success"] else "❌ 失败"
        print(
            f"{test_name:<25} {status:<8} {result['duration']:<10.2f} {result['output_files']}"
        )

    # 性能分析
    successful_tests = {k: v for k, v in results.items() if v["success"]}

    if len(successful_tests) > 1:
        print(f"\n{'='*60}")
        print("📊 性能分析")
        print(f"{'='*60}")

        # 找到最快的配置
        fastest = min(successful_tests.items(), key=lambda x: x[1]["duration"])
        print(f"⚡ 最快配置: {fastest[0]} ({fastest[1]['duration']:.2f}秒)")

        # 计算性能差异
        durations = [v["duration"] for v in successful_tests.values()]
        avg_duration = sum(durations) / len(durations)
        print(f"📈 平均处理时间: {avg_duration:.2f}秒")

        print(f"\n💡 建议:")
        if fastest[1]["duration"] < avg_duration * 0.9:
            print(f"   推荐使用 '{fastest[0]}' 配置以获得最佳性能")
        else:
            print(f"   各配置性能相近，建议使用 '自动批处理大小' 以获得最佳内存使用")

    print(f"\n✅ 流式处理测试完成!")
    print(f"📁 测试结果保存在: {test_output_dir}")


if __name__ == "__main__":
    main()
