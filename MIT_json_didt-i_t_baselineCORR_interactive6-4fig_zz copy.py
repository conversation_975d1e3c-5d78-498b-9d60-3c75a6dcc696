# Add this to the very top of your script, before other imports
import matplotlib

matplotlib.use("TkAgg")

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import time
import sys
import traceback
import json
import matplotlib.cm as cm
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import threading
import multiprocessing
from datetime import datetime
import logging
from scipy.signal import savgol_filter
from scipy.interpolate import interp1d

# Set up logging with rotation
LOG_FILE = "battery_analysis.log"
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="[%(asctime)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

# Maximum log file size (10 MB)
MAX_LOG_SIZE = 10 * 1024 * 1024


def rotate_log_if_needed():
    """Rotate log file if it gets too large"""
    if os.path.exists(LOG_FILE) and os.path.getsize(LOG_FILE) > MAX_LOG_SIZE:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        backup_log = f"{LOG_FILE}.{timestamp}"
        try:
            os.rename(LOG_FILE, backup_log)
        except Exception:
            pass  # Continue with current log if rotation fails


def log(message, text_widget=None):
    """Record log messages and display in GUI"""
    rotate_log_if_needed()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    log_message = f"[{timestamp}] {message}"
    print(log_message)
    logging.info(message)

    # Update GUI if text_widget provided
    if text_widget:
        text_widget.config(state=tk.NORMAL)
        text_widget.insert(tk.END, log_message + "\n")
        text_widget.see(tk.END)
        text_widget.config(state=tk.DISABLED)


def compute_dIdt_vectorized(time_s, current_A, internal=50):
    """Calculate dI/dt using vectorized method for better performance"""
    if len(time_s) < 2:
        return None, None

    try:
        # Convert to numpy arrays for vector operations
        time_array = np.array(time_s)
        current_array = np.array(current_A)

        # Calculate time differences and current differences
        dt = np.diff(time_array)
        dI = np.diff(current_array)

        # 大幅降低阈值，使更多数据点被保留
        # Calculate threshold - 使用更低的internal值来降低阈值
        max_current = np.max(np.abs(current_array))
        threshold = max_current / (internal * 10)  # 将阈值降低10倍

        # 只过滤掉时间差无效的点
        valid_mask = np.abs(dt) > 1e-10

        # 计算所有有效点的dI/dt
        dIdt_values = dI[valid_mask] / dt[valid_mask]
        didt_times = time_array[:-1][valid_mask]

        if len(dIdt_values) == 0:
            return None, None

        # Fix: Ensure we're not returning NaN or infinite values
        valid_values = ~np.isnan(dIdt_values) & ~np.isinf(dIdt_values)
        if not np.all(valid_values):
            dIdt_values = dIdt_values[valid_values]
            didt_times = didt_times[valid_values]

        if len(dIdt_values) == 0:
            return None, None

        # 对dI/dt值进行平滑处理，减少噪声
        if len(dIdt_values) > 5:
            window_size = min(5, len(dIdt_values) - (len(dIdt_values) % 2 == 0))
            if window_size >= 3:
                try:
                    from scipy.signal import savgol_filter

                    dIdt_values = savgol_filter(dIdt_values, window_size, 2)
                except Exception:
                    pass  # 如果平滑失败，使用原始值

        # 确保返回的是numpy数组而不是视图
        return np.array(dIdt_values), np.array(didt_times)
    except Exception as e:
        log(f"Error in compute_dIdt_vectorized: {str(e)}")
        return None, None


def compute_dIdt(time_s, current_A, internal=50, use_vectorized=True):
    """Calculate dI/dt using threshold-based method"""
    if use_vectorized:
        return compute_dIdt_vectorized(time_s, current_A, internal)

    if len(time_s) < 2:
        return None, None

    # Initialize arrays
    dIdt = []
    time_diff = []

    try:
        # Convert to numpy arrays for better performance
        time_array = time_s.values
        current_array = current_A.values

        # Calculate time span and threshold
        max_current = np.max(np.abs(current_array))
        threshold = max_current / internal

        i = 0  # Current start index
        while i < len(time_array) - 1:
            # Find step size n
            n = 1
            while (i + n < len(time_array)) and (
                np.abs(current_array[i + n] - current_array[i]) <= threshold
            ):
                n += 1
                # Safety check to avoid infinite loop
                if n > 100:
                    break

            # Prevent index out of bounds
            n = min(n, len(time_array) - i - 1)

            if n > 0:  # Ensure valid step size
                denominator = time_array[i + n] - time_array[i]
                if abs(denominator) > 1e-10:  # Avoid division by very small numbers
                    dIdt_value = (current_array[i + n] - current_array[i]) / denominator
                    dIdt.append(dIdt_value)
                    time_diff.append(time_array[i])

            # Move to next point
            i += max(1, n // 2)

    except Exception as e:
        log(f"Error in compute_dIdt: {str(e)}")
        return None, None

    # Check calculation results
    if len(dIdt) == 0:
        return None, None

    return dIdt, time_diff


def process_dIdt_data(
    all_cycles_data, text_widget=None, downsample_factor=1, use_multiprocessing=False
):
    """Process all cycle data, calculate dI/dt with optional downsampling and multiprocessing"""
    log("Calculating dI/dt for all cycles...", text_widget)
    didt_cycles = {}

    # Check if multiprocessing should be used
    if use_multiprocessing and len(all_cycles_data) > 5:
        return process_dIdt_data_parallel(
            all_cycles_data, text_widget, downsample_factor
        )

    # Create a progress tracking function for UI updates
    total_cycles = len(all_cycles_data)
    processed_cycles = 0

    for cycle, data in all_cycles_data.items():
        # Apply downsampling if requested
        if downsample_factor > 1:
            indices = np.arange(0, len(data), downsample_factor)
            if len(indices) > 0:
                data = data.iloc[indices].copy()

        # Extract time and current data
        time_s = data["Time(s)"]
        current_A = data["Current(A)"]

        # Calculate dI/dt
        didt_values, didt_times = compute_dIdt(time_s, current_A, use_vectorized=True)

        if didt_values is not None and didt_times is not None:
            # Create dI/dt dataframe
            df_didt = pd.DataFrame(
                {"Time(s)": didt_times, "dI/dt_raw(A/s)": didt_values}
            )

            # Add smoothed dI/dt column (using rolling window of 5)
            df_didt["dI/dt_smooth(A/s)"] = (
                df_didt["dI/dt_raw(A/s)"].rolling(window=5, center=True).mean()
            )

            # Fill NaN values (at beginning and end of window)
            df_didt["dI/dt_smooth(A/s)"] = df_didt["dI/dt_smooth(A/s)"].fillna(
                df_didt["dI/dt_raw(A/s)"]
            )

            # Store processed data
            didt_cycles[cycle] = df_didt

            log(
                f"  Processed cycle {cycle}: calculated {len(df_didt)} dI/dt data points",
                text_widget,
            )
        else:
            log(f"  Warning: Unable to calculate dI/dt for cycle {cycle}", text_widget)

        # Update progress
        processed_cycles += 1
        if processed_cycles % 10 == 0 or processed_cycles == total_cycles:
            progress = (processed_cycles / total_cycles) * 100
            log(
                f"  Progress: {progress:.1f}% ({processed_cycles}/{total_cycles} cycles)",
                text_widget,
            )

    log(f"dI/dt calculation complete, processed {len(didt_cycles)} cycles", text_widget)
    return didt_cycles


# Function for processing a single cycle (to be used with multiprocessing)


def process_cycle(cycle_data):
    cycle_num, data, downsample_factor = cycle_data

    # Apply downsampling if requested
    if downsample_factor > 1:
        indices = np.arange(0, len(data), downsample_factor)
        if len(indices) > 0:
            data = data.iloc[indices].copy()

    # Extract time and current data
    time_s = data["Time(s)"]  # 这里已经是秒为单位的时间
    current_A = data["Current(A)"]

    # 简化调试信息，只在第一个循环或特定循环输出
    if cycle_num <= 3 or cycle_num % 50 == 0:
        print(
            f"Processing cycle {cycle_num}: {len(data)} data points, time range: {time_s.min():.1f}s - {time_s.max():.1f}s"
        )

    # Calculate dI/dt
    didt_values, didt_times = compute_dIdt_vectorized(time_s, current_A)

    if didt_values is not None and didt_times is not None:
        # 只在第一个循环或特定循环输出调试信息
        if cycle_num <= 3 or cycle_num % 50 == 0:
            print(
                f"  Cycle {cycle_num}: {len(didt_values)} dI/dt points, range: {min(didt_values):.3f} to {max(didt_values):.3f} A/s"
            )

        # 确保时间单位是秒
        # Create dI/dt dataframe
        df_dict = {"Time(s)": didt_times, "dI/dt_raw(A/s)": didt_values}

        # 如果原始数据中有分钟时间，也保存下来
        if "Time(min)" in data.columns:
            # 通过插值找到对应的分钟时间
            # 创建一个从秒到分钟的映射函数
            if len(data["Time(s)"]) > 1 and len(data["Time(min)"]) > 1:
                try:
                    sec_to_min = interp1d(
                        data["Time(s)"].values,
                        data["Time(min)"].values,
                        bounds_error=False,
                        fill_value="extrapolate",
                    )
                    # 计算对应的分钟时间
                    min_times = sec_to_min(didt_times)
                    df_dict["Time(min)"] = min_times
                except Exception as e:
                    if cycle_num <= 3:
                        print(
                            f"  Warning: Could not interpolate minute times for cycle {cycle_num}"
                        )

        # Convert to pandas dataframe
        df_didt = pd.DataFrame(df_dict)

        # Add smoothed dI/dt using rolling window
        df_didt["dI/dt_smooth(A/s)"] = (
            df_didt["dI/dt_raw(A/s)"].rolling(window=5, center=True).mean()
        )

        # Fill NaN values
        df_didt["dI/dt_smooth(A/s)"] = df_didt["dI/dt_smooth(A/s)"].fillna(
            df_didt["dI/dt_raw(A/s)"]
        )

        # 确保数据按时间排序
        df_didt = df_didt.sort_values(by="Time(s)")

        return cycle_num, df_didt
    else:
        if cycle_num <= 10 or cycle_num % 20 == 0:
            print(f"  Warning: No valid dI/dt data for cycle {cycle_num}")
        return cycle_num, None


def process_dIdt_data_parallel(all_cycles_data, text_widget=None, downsample_factor=1):
    """Process dI/dt calculations using parallel processing for better performance"""
    log("Calculating dI/dt using parallel processing...", text_widget)

    # Create a pool of workers
    num_workers = min(multiprocessing.cpu_count(), len(all_cycles_data))
    log(f"Using {num_workers} CPU cores for parallel processing", text_widget)

    # Prepare data for parallel processing
    cycle_data_list = [(cycle, data) for cycle, data in all_cycles_data.items()]

    # Process data in parallel
    didt_cycles = {}
    with multiprocessing.Pool(num_workers) as pool:
        results = list(
            pool.map(
                process_cycle, [(c, d, downsample_factor) for c, d in cycle_data_list]
            )
        )

    # Collect results
    for cycle_num, df_didt in results:
        if df_didt is not None:
            didt_cycles[cycle_num] = df_didt
            log(
                f"  Processed cycle {cycle_num}: calculated {len(df_didt)} dI/dt data points",
                text_widget,
            )
        else:
            log(
                f"  Warning: Unable to calculate dI/dt for cycle {cycle_num}",
                text_widget,
            )

    log(
        f"Parallel dI/dt calculation complete, processed {len(didt_cycles)} cycles",
        text_widget,
    )
    return didt_cycles


def detect_knee_points(cycle_capacities, min_pts=3):
    """Automatically detect knee points in capacity data using curvature analysis"""
    if not cycle_capacities or len(cycle_capacities) < min_pts:
        return []

    try:
        # Sort cycles
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        if len(cycles) < 5:
            return []  # Not enough data for reliable detection

        # Convert to numpy arrays for numerical operations
        x = np.array(cycles)
        y = np.array(capacities)

        # Apply Savitzky-Golay filter to smooth the data
        if len(x) >= 5:
            window_size = min(
                len(x) - (len(x) % 2 == 0), 5
            )  # Make sure window size is odd and <= len(x)
            if window_size >= 3:
                y_smooth = savgol_filter(
                    y, window_size, 2
                )  # Window size 5, polynomial order 2
            else:
                y_smooth = y
        else:
            y_smooth = y

        # Calculate first and second derivatives
        dy = np.gradient(y_smooth)
        d2y = np.gradient(dy)

        # Calculate curvature: κ = |y''| / (1 + y'^2)^(3/2)
        curvature = np.abs(d2y) / (1 + dy**2) ** 1.5

        # Find local maxima in curvature above threshold (knee points)
        knee_indices = []
        threshold = 0.1 * np.max(curvature)

        # Skip first and last few points to avoid edge effects
        for i in range(2, len(curvature) - 2):
            if (
                curvature[i] > threshold
                and curvature[i] > curvature[i - 1]
                and curvature[i] > curvature[i + 1]
            ):
                knee_indices.append(i)

        # Limit to top 3 knee points
        if knee_indices:
            # Sort by curvature value (descending)
            knee_indices = sorted(
                knee_indices, key=lambda i: curvature[i], reverse=True
            )
            knee_indices = knee_indices[:3]  # Take top 3

        knee_cycles = [cycles[i] for i in knee_indices]
        return knee_cycles

    except Exception as e:
        log(f"Error in knee point detection: {str(e)}")
        return []


def export_all_cycles_pdf(
    all_cycles_data,
    didt_cycles_data,
    output_folder,
    sample_name,
    text_widget=None,
    cycles_to_include=None,
    page_size=(10, 12),
    dpi=300,
):
    """Export all or selected cycle data to PDF with improved formatting"""
    log(f"Exporting cycle data to PDF...", text_widget)

    # Create output folder (if it doesn't exist)
    os.makedirs(output_folder, exist_ok=True)

    # Get available cycles
    if cycles_to_include:
        available_cycles = [
            c for c in sorted(all_cycles_data.keys()) if c in cycles_to_include
        ]
        pdf_filename = os.path.join(output_folder, f"{sample_name}_selected_cycles.pdf")
    else:
        available_cycles = sorted(list(all_cycles_data.keys()))
        pdf_filename = os.path.join(output_folder, f"{sample_name}_all_cycles.pdf")

    # Calculate axis limits for consistent plots
    max_time = 0
    max_current = 0
    min_didt = 0

    for cycle in available_cycles:
        if cycle in all_cycles_data:
            data = all_cycles_data[cycle]
            if len(data) > 0:
                max_time = max(max_time, data["Time(s)"].max())
                max_current = max(max_current, data["Current(A)"].max())

        if cycle in didt_cycles_data:
            data = didt_cycles_data[cycle]
            if len(data) > 0:
                min_didt = min(min_didt, data["dI/dt_smooth(A/s)"].min())

    # Create PDF file with metadata
    with PdfPages(pdf_filename) as pdf:
        # Set metadata
        d = pdf.infodict()
        d["Title"] = f"Battery Analysis: {sample_name}"
        d["Author"] = "Battery Analysis Tool"
        d["Subject"] = "Cycle Data Analysis"
        d["CreationDate"] = datetime.today()
        d["ModDate"] = datetime.today()

        # Track progress
        total_cycles = len(available_cycles)
        for i, cycle in enumerate(available_cycles):
            # Create new figure with better formatting
            plt.rcParams["font.family"] = "Arial"
            fig, axs = plt.subplots(2, 1, figsize=page_size)
            fig.suptitle(f"{sample_name} - Cycle {cycle}", fontsize=16, y=0.98)

            # Plot current graph
            if cycle in all_cycles_data:
                data = all_cycles_data[cycle]

                # Downsample if too many points (for display)
                if len(data) > 5000:
                    step = len(data) // 5000
                    plot_data = data.iloc[::step]
                else:
                    plot_data = data

                axs[0].plot(
                    plot_data["Time(s)"], plot_data["Current(A)"], "b-", linewidth=1.5
                )

                # Set consistent limits for all plots
                time_limit = min(max_time, data["Time(s)"].max() * 1.2)  # 20% margin
                current_limit = min(
                    max_current * 1.2, data["Current(A)"].max() * 1.5
                )  # 20-50% margin

                axs[0].set_xlim(0, time_limit)
                axs[0].set_ylim(0, current_limit)
                axs[0].set_xlabel("Time (s)", fontsize=12)
                axs[0].set_ylabel("Current (A)", fontsize=12)
                axs[0].set_title(f"Current vs Time", fontsize=14)
                axs[0].grid(True, alpha=0.3)

                # Add reference lines at 25% and 75% of axis limits
                axs[0].axhline(
                    y=current_limit * 0.25, color="black", linestyle="--", alpha=0.5
                )
                axs[0].axhline(
                    y=current_limit * 0.75, color="black", linestyle="--", alpha=0.5
                )
                axs[0].axvline(
                    x=time_limit * 0.15, color="black", linestyle="--", alpha=0.5
                )
                axs[0].axvline(
                    x=time_limit * 0.75, color="black", linestyle="--", alpha=0.5
                )

            # Plot dI/dt graph
            if cycle in didt_cycles_data:
                didt_data = didt_cycles_data[cycle]

                # Downsample if too many points
                if len(didt_data) > 5000:
                    step = len(didt_data) // 5000
                    plot_didt_data = didt_data.iloc[::step]
                else:
                    plot_didt_data = didt_data

                axs[1].plot(
                    plot_didt_data["Time(s)"],
                    plot_didt_data["dI/dt_smooth(A/s)"],
                    "r-",
                    linewidth=1.5,
                )

                # Set consistent limits
                axs[1].set_xlim(0, time_limit)
                didt_limit = max(abs(min_didt * 1.2), 0.001)  # 20% margin
                axs[1].set_ylim(-didt_limit, 0)
                axs[1].set_xlabel("Time (s)", fontsize=12)
                axs[1].set_ylabel("dI/dt (A/s)", fontsize=12)
                axs[1].set_title(f"dI/dt vs Time", fontsize=14)
                axs[1].grid(True, alpha=0.3)

                # Add reference lines
                axs[1].axvline(
                    x=time_limit * 0.15, color="black", linestyle="--", alpha=0.5
                )
                axs[1].axvline(
                    x=time_limit * 0.75, color="black", linestyle="--", alpha=0.5
                )

            # Add cycle information
            cycle_info = (
                f"Cycle: {cycle}/{total_cycles} ({i+1}/{len(available_cycles)})"
            )
            fig.text(0.02, 0.02, cycle_info, fontsize=9)

            # Adjust layout
            plt.tight_layout(rect=[0, 0.03, 1, 0.95])

            # Save to PDF with high DPI
            pdf.savefig(fig, dpi=dpi)
            plt.close(fig)

            if (i + 1) % 10 == 0 or i == len(available_cycles) - 1:
                progress = ((i + 1) / len(available_cycles)) * 100
                log(
                    f"  Progress: {progress:.1f}% ({i+1}/{len(available_cycles)} cycles)",
                    text_widget,
                )

    log(f"PDF export complete: {pdf_filename}", text_widget)
    return pdf_filename


def extract_cycles_from_json(json_data, text_widget=None):
    """Extract cycle data from JSON data structure with improved error handling"""
    log("Extracting cycle data from JSON...", text_widget)

    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}

    # 检查数据格式：支持数组格式（CV数据）和字典格式（原格式）
    if isinstance(json_data, list):
        # CV JSON数据格式（数组）
        log("Detected CV JSON format (array)", text_widget)

        for cycle_data in json_data:
            try:
                cycle_num = cycle_data["cycle"]

                # 提取CV阶段数据
                cv_phase = cycle_data["cv_phase"]
                time_s = cv_phase["time"]
                current_A = [c / 1000 for c in cv_phase["current"]]  # mA转A

                if len(time_s) > 0 and len(current_A) > 0:
                    # 创建DataFrame
                    df = pd.DataFrame({"Time(s)": time_s, "Current(A)": current_A})

                    all_cycles_data[cycle_num] = df
                    cycle_capacities[cycle_num] = cycle_data["QD"] / 1000  # mAh转Ah
                    cycle_ce_values[cycle_num] = cycle_data["CE"]

                    log(
                        f"  Extracted cycle {cycle_num}, containing {len(df)} data points",
                        text_widget,
                    )

            except (KeyError, ValueError, TypeError) as e:
                log(f"  Error processing cycle data: {str(e)}", text_widget)
                continue

    elif isinstance(json_data, dict):
        # 原始JSON格式（字典）
        log("Detected original JSON format (dictionary)", text_widget)

        # 原有的字典处理逻辑
        for key in json_data.keys():
            if key.startswith("Cycle_"):
                try:
                    cycle_num = int(key.split("_")[1])
                    cycle_data = json_data[key]

                    if not isinstance(cycle_data, dict):
                        log(
                            f"  Warning: Invalid data format for cycle {key}",
                            text_widget,
                        )
                        continue

                    time_min = cycle_data.get("relative_time_min", [])
                    current_A = cycle_data.get("current_A", [])

                    if not time_min or not current_A:
                        log(
                            f"  Warning: Missing time or current data for cycle {key}",
                            text_widget,
                        )
                        continue

                    if len(time_min) != len(current_A):
                        log(
                            f"  Warning: Time and current arrays have different lengths in cycle {key}",
                            text_widget,
                        )
                        min_len = min(len(time_min), len(current_A))
                        time_min = time_min[:min_len]
                        current_A = current_A[:min_len]

                    if len(time_min) > 0 and len(current_A) > 0:
                        start_time = time_min[0]
                        time_s = [(t - start_time) * 60 for t in time_min]

                        df = pd.DataFrame({"Time(s)": time_s, "Current(A)": current_A})
                        df["Time(min)"] = [t - start_time for t in time_min]

                        all_cycles_data[cycle_num] = df
                        log(
                            f"  Extracted cycle {cycle_num}, containing {len(df)} data points",
                            text_widget,
                        )

                except (ValueError, IndexError, TypeError) as e:
                    log(f"  Error processing cycle {key}: {str(e)}", text_widget)
                    continue

        # 处理CE数据（原格式）
        if "CE" in json_data:
            try:
                ce_values = json_data["CE"]
                if isinstance(ce_values, list):
                    for i, ce in enumerate(ce_values):
                        cycle_num = i + 1
                        if cycle_num in all_cycles_data:
                            try:
                                ce_value = float(ce)
                                cycle_ce_values[cycle_num] = ce_value
                                log(
                                    f"  Extracted CE for cycle {cycle_num}: {ce_value:.6f}",
                                    text_widget,
                                )
                            except (ValueError, TypeError):
                                log(
                                    f"  Warning: Non-numeric CE value for cycle {cycle_num}",
                                    text_widget,
                                )
                else:
                    log(f"  Warning: 'CE' is not a list", text_widget)
            except Exception as e:
                log(f"  Error processing CE data: {str(e)}", text_widget)
    else:
        log(
            "Error: Invalid JSON data format - expected a dictionary or array",
            text_widget,
        )
        return None, None, None

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found", text_widget)
    return all_cycles_data, cycle_capacities, cycle_ce_values


def extract_cycles_from_csv(csv_data, text_widget=None):
    """Extract cycle data from CSV data with enhanced validation and error handling"""
    log("Extracting cycle data from CSV...", text_widget)

    all_cycles_data = {}
    cycle_capacities = {}

    # Check if required columns exist
    required_columns = ["Time(s)", "Current(A)"]
    for col in required_columns:
        if col not in csv_data.columns:
            log(f"Error: Required column '{col}' missing in data file", text_widget)
            return None, None

    # Verify data types and convert if needed
    try:
        csv_data["Time(s)"] = pd.to_numeric(csv_data["Time(s)"], errors="coerce")
        csv_data["Current(A)"] = pd.to_numeric(csv_data["Current(A)"], errors="coerce")

        # Check for NaN values after conversion
        nan_times = csv_data["Time(s)"].isna().sum()
        nan_currents = csv_data["Current(A)"].isna().sum()

        if nan_times > 0:
            log(
                f"Warning: Found {nan_times} non-numeric time values (will be dropped)",
                text_widget,
            )
        if nan_currents > 0:
            log(
                f"Warning: Found {nan_currents} non-numeric current values (will be dropped)",
                text_widget,
            )

        # Drop rows with NaN values
        csv_data = csv_data.dropna(subset=["Time(s)", "Current(A)"]).reset_index(
            drop=True
        )

    except Exception as e:
        log(f"Error converting data columns: {str(e)}", text_widget)
        return None, None

    # Check if Cycle column exists
    if "Cycle" in csv_data.columns:
        try:
            # Convert Cycle column to numeric if it's not
            csv_data["Cycle"] = pd.to_numeric(csv_data["Cycle"], errors="coerce")

            # Check for invalid cycle numbers
            invalid_cycles = csv_data["Cycle"].isna().sum()
            if invalid_cycles > 0:
                log(
                    f"Warning: Found {invalid_cycles} rows with invalid cycle numbers",
                    text_widget,
                )
                csv_data = csv_data.dropna(subset=["Cycle"]).reset_index(drop=True)

            # Convert cycle numbers to integers
            csv_data["Cycle"] = csv_data["Cycle"].astype(int)

            # Group data by cycle
            for cycle, group in csv_data.groupby("Cycle"):
                # Create a copy of the group data
                cycle_data = group.copy()

                # Normalize time to start from 0 for each cycle
                if len(cycle_data) > 0:
                    min_time = cycle_data["Time(s)"].min()
                    cycle_data["Time(s)"] = cycle_data["Time(s)"] - min_time

                all_cycles_data[cycle] = cycle_data

                # Calculate capacity for this cycle (Ah)
                # Integrate current over time: capacity = ∫I·dt
                capacity = 0
                time_values = cycle_data["Time(s)"].values
                current_values = cycle_data["Current(A)"].values

                for i in range(1, len(time_values)):
                    dt = (
                        time_values[i] - time_values[i - 1]
                    ) / 3600  # convert seconds to hours
                    avg_current = (
                        current_values[i] + current_values[i - 1]
                    ) / 2  # average current
                    capacity += avg_current * dt

                # Store capacity for this cycle
                cycle_capacities[cycle] = abs(capacity)

                log(
                    f"  Extracted cycle {cycle}, containing {len(cycle_data)} data points, capacity: {abs(capacity):.4f} Ah",
                    text_widget,
                )
        except Exception as e:
            log(f"Error processing cycle data: {str(e)}", text_widget)
            log(traceback.format_exc(), text_widget)
            return None, None
    else:
        # If no Cycle column, treat all data as one cycle
        cycle_data = csv_data.copy()

        # Normalize time to start from 0
        if len(cycle_data) > 0:
            min_time = cycle_data["Time(s)"].min()
            cycle_data["Time(s)"] = cycle_data["Time(s)"] - min_time

        all_cycles_data[1] = cycle_data

        # Calculate capacity
        capacity = 0
        time_values = cycle_data["Time(s)"].values
        current_values = cycle_data["Current(A)"].values

        for i in range(1, len(time_values)):
            dt = (
                time_values[i] - time_values[i - 1]
            ) / 3600  # convert seconds to hours
            avg_current = (
                current_values[i] + current_values[i - 1]
            ) / 2  # average current
            capacity += avg_current * dt

        # Store capacity
        cycle_capacities[1] = abs(capacity)

        log(
            f"  Data file has no Cycle column, treating all {len(csv_data)} data points as cycle 1, capacity: {abs(capacity):.4f} Ah",
            text_widget,
        )

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found", text_widget)
    return all_cycles_data, cycle_capacities


def extract_cycles_from_mit_cv_json(json_data, text_widget=None, selected_cell=None):
    """Extract cycle data from MIT CV JSON format"""
    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}
    
    try:
        # Get available battery cells
        available_cells = [key for key in json_data.keys() if key.startswith('b3c')]
        
        if not available_cells:
            log("No battery cells found in MIT CV data", text_widget)
            return {}, {}, {}
        
        # Use selected cell or first available cell
        if selected_cell and selected_cell in available_cells:
            cell_key = selected_cell
        else:
            cell_key = available_cells[0]
            
        log(f"Processing battery cell: {cell_key}", text_widget)
        log(f"Available cells: {', '.join(available_cells)}", text_widget)
        
        cell_data = json_data[cell_key]
        
        if 'cv_cycles' not in cell_data or 'summary' not in cell_data:
            log(f"Missing cv_cycles or summary data for {cell_key}", text_widget)
            return {}, {}, {}
        
        cv_cycles = cell_data['cv_cycles']
        summary = cell_data['summary']
        
        # Extract CE values from summary
        ce_values = summary.get('CE', [])
        qd_values = summary.get('QD', [])
        
        log(f"Processing {len(cv_cycles)} cycles from {cell_key}", text_widget)
        
        # Process each cycle
        for cycle_str, cycle_data in cv_cycles.items():
            try:
                cycle_num = int(cycle_str)
                
                # Extract CV phase data
                time_min = np.array(cycle_data['t'])  # Time in minutes
                current_A = np.array(cycle_data['I'])
                voltage_V = np.array(cycle_data['V'])
                capacity_Ah = np.array(cycle_data['Qc'])
                
                if len(time_min) > 0 and len(current_A) > 0:
                    # Normalize time to start from 0 and convert to seconds
                    time_min_normalized = time_min - time_min[0]  # Start from 0
                    time_s = time_min_normalized * 60  # Convert minutes to seconds
                    
                    # Create DataFrame
                    df = pd.DataFrame({
                        "Time(s)": time_s,
                        "Time(min)": time_min_normalized,
                        "Current(A)": current_A,
                        "Voltage(V)": voltage_V,
                        "Capacity(Ah)": capacity_Ah
                    })
                    
                    all_cycles_data[cycle_num] = df
                    
                    # Extract capacity (use final capacity value)
                    if len(capacity_Ah) > 0:
                        cycle_capacities[cycle_num] = capacity_Ah[-1]
                    elif cycle_num < len(qd_values):
                        cycle_capacities[cycle_num] = qd_values[cycle_num] / 1000  # Convert mAh to Ah
                    
                    # Extract CE value if available
                    if cycle_num < len(ce_values):
                        cycle_ce_values[cycle_num] = ce_values[cycle_num]
                    
                    log(f"  Extracted cycle {cycle_num}: {len(df)} points, time range: 0-{time_s[-1]:.1f}s", text_widget)
            
            except (KeyError, ValueError, TypeError) as e:
                log(f"  Error processing cycle {cycle_str}: {str(e)}", text_widget)
                continue
        
        log(f"Successfully extracted {len(all_cycles_data)} cycles from {cell_key}", text_widget)
        
    except Exception as e:
        log(f"Error processing MIT CV JSON data: {str(e)}", text_widget)
        return {}, {}, {}
    
    return all_cycles_data, cycle_capacities, cycle_ce_values

def detect_mit_cv_format(json_data):
    """Detect if JSON data is in MIT CV format"""
    if not isinstance(json_data, dict):
        return False
    
    # Check for battery cell keys (b3c0, b3c1, etc.)
    for key, value in json_data.items():
        if key.startswith('b3c') and isinstance(value, dict):
            if 'cv_cycles' in value and 'summary' in value:
                return True
    
    return False

def get_available_battery_cells(json_data):
    """Get list of available battery cells from MIT CV data"""
    if not isinstance(json_data, dict):
        return []
    
    return [key for key in json_data.keys() if key.startswith('b3c')]


class PlotManager:
    """Manage plotting operations with consistent styling"""
    
    def __init__(self):
        self.color_map = plt.cm.get_cmap('viridis')
    
    def plot_cycle_current(self, ax, cycle_data, cycle_num, color=None, label=None):
        """Plot current vs time for a cycle"""
        if color is None:
            color = self.color_map(cycle_num % 10)

        if label is None:
            label = f"Cycle {cycle_num}"

        # Downsample if too many points
        if len(cycle_data) > 5000:
            step = len(cycle_data) // 5000
            plot_data = cycle_data.iloc[::step]
        else:
            plot_data = cycle_data

        line, = ax.plot(
            plot_data["Time(s)"],
            plot_data["Current(A)"],
            "-",
            color=color,
            label=label,
            linewidth=1.5,
        )

        return line

    def plot_cycle_didt(self, ax, didt_data, cycle_num, color=None, linestyle="-", alpha=0.7, zorder=5):
        """Plot dI/dt vs time for a cycle"""
        if didt_data is None or len(didt_data) == 0:
            return None
        if color is None:
            color = self.color_map(cycle_num % 10)

        # Downsample if too many points
        if len(didt_data) > 5000:
            step = len(didt_data) // 5000
            plot_data = didt_data.iloc[::step]
        else:
            plot_data = didt_data
        
        # Plot dI/dt data
        if "dI/dt_smooth(A/s)" in plot_data.columns:
            time_values = plot_data["Time(s)"]
            didt_values = plot_data["dI/dt_smooth(A/s)"]

            # Filter out invalid values
            valid_mask = (~np.isnan(didt_values)) & (~np.isinf(didt_values))
            if not all(valid_mask):
                time_values = time_values[valid_mask]
                didt_values = didt_values[valid_mask]

            line, = ax.plot(
                time_values,
                didt_values,
                linestyle=linestyle,
                linewidth=1.5,
                color=color,
                alpha=alpha,
                zorder=zorder,
            )

            ax.set_xlabel("Time (s)")
            ax.set_ylabel("dI/dt (A/s)")
            return line
        else:
            print(f"Warning: No dI/dt column found in data for cycle {cycle_num}")
            return None

    def plot_capacities(self, ax, cycle_capacities, knee_points=None, point_colors=None):
        """Plot capacity vs cycle number with optional knee points"""
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        # Plot capacity curve
        line, = ax.plot(cycles, capacities, "o-", markersize=3, linewidth=1.5)

        # Add trend line (moving average)
        if len(cycles) > 5:
            window = min(5, len(cycles))
            trend = pd.Series(capacities).rolling(window=window, center=True).mean()
            ax.plot(cycles, trend, "r--", linewidth=1.5, alpha=0.7, label="Trend")

        # Mark knee points if provided
        if knee_points:
            for knee in knee_points:
                if knee in cycle_capacities:
                    ax.plot([knee], [cycle_capacities[knee]], "ro", markersize=8, 
                           markerfacecolor='red', markeredgecolor='black', 
                           markeredgewidth=1, label=f"Knee {knee}")

        ax.set_xlabel("Cycle Number")
        ax.set_ylabel("Capacity (Ah)")
        ax.grid(True, alpha=0.3)
        
        return line


class BatteryAnalysisApp:
    """Main application class for the battery analysis tool"""

    def __init__(self, root):
        self.root = root
        self.root.title("Battery Analysis Tool")
        self.root.geometry("1400x900")

        # Data storage
        self.all_cycles_data = {}
        self.cycle_capacities = {}
        self.cycle_ce_values = {}
        self.didt_cycles_data = {}
        self.sample_name = ""
        self.auto_detected_knee_points = set()
        self.marked_knee_points = set()
        self.current_json_data = None  # Store loaded JSON data
        self.available_cells = []      # Store available battery cells

        # Initialize UI variables
        self.file_path_var = tk.StringVar()
        self.sample_name_var = tk.StringVar(value="Sample")
        self.progress_var = tk.DoubleVar(value=0)
        
        # Advanced options variables
        self.didt_internal_var = tk.BooleanVar(value=True)
        self.downsample_var = tk.IntVar(value=1)
        self.use_multiprocessing_var = tk.BooleanVar(value=False)
        self.auto_detect_knees_var = tk.BooleanVar(value=True)

        self.setup_ui()

    def setup_ui(self):
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # File loading
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(file_frame, text="Load File", command=self.load_file).pack(side=tk.LEFT)
        
        # Battery cell selection (initially hidden)
        self.cell_frame = ttk.Frame(control_frame)
        
        ttk.Label(self.cell_frame, text="Battery Cell:").pack(side=tk.LEFT, padx=(10, 5))
# Add this to the very top of your script, before other imports
import matplotlib
matplotlib.use("TkAgg")

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import time
import sys
import traceback
import json
import matplotlib.cm as cm
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import threading
import multiprocessing
from datetime import datetime
import logging
from scipy.signal import savgol_filter
from scipy.interpolate import interp1d

def log(message, text_widget=None):
    """Log message to console and optionally to text widget"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"
    print(formatted_message)
    
    if text_widget:
        try:
            text_widget.insert(tk.END, formatted_message + "\n")
            text_widget.see(tk.END)
            text_widget.update_idletasks()
        except:
            pass

def compute_dIdt_vectorized(time_s, current_A, internal=50):
    """Vectorized calculation of dI/dt"""
    try:
        # Convert to numpy arrays for vector operations
        time_array = np.array(time_s)
        current_array = np.array(current_A)

        # Calculate time differences and current differences
        dt = np.diff(time_array)
        dI = np.diff(current_array)

        # Calculate threshold
        max_current = np.max(np.abs(current_array))
        threshold = max_current / (internal * 10)

        # Only filter out points with invalid time differences
        valid_mask = np.abs(dt) > 1e-10

        # Calculate dI/dt for all valid points
        dIdt_values = dI[valid_mask] / dt[valid_mask]
        didt_times = time_array[:-1][valid_mask]

        if len(dIdt_values) == 0:
            return None, None

        return dIdt_values, didt_times

    except Exception as e:
        print(f"Error in compute_dIdt_vectorized: {str(e)}")
        return None, None

def extract_cycles_from_mit_cv_json(json_data, text_widget=None, selected_cell=None):
    """Extract cycle data from MIT CV JSON format"""
    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}
    
    try:
        # Get available battery cells
        available_cells = [key for key in json_data.keys() if key.startswith('b3c')]
        
        if not available_cells:
            log("No battery cells found in MIT CV data", text_widget)
            return {}, {}, {}
        
        # Use selected cell or first available cell
        if selected_cell and selected_cell in available_cells:
            cell_key = selected_cell
        else:
            cell_key = available_cells[0]
            
        log(f"Processing battery cell: {cell_key}", text_widget)
        
        cell_data = json_data[cell_key]
        
        if 'cv_cycles' not in cell_data or 'summary' not in cell_data:
            log(f"Missing cv_cycles or summary data for {cell_key}", text_widget)
            return {}, {}, {}
        
        cv_cycles = cell_data['cv_cycles']
        summary = cell_data['summary']
        
        # Extract CE values from summary
        ce_values = summary.get('CE', [])
        qd_values = summary.get('QD', [])
        
        log(f"Processing {len(cv_cycles)} cycles from {cell_key}", text_widget)
        
        # Process each cycle
        for cycle_str, cycle_data in cv_cycles.items():
            try:
                cycle_num = int(cycle_str)
                
                # Extract CV phase data
                time_min = np.array(cycle_data['t'])
                current_A = np.array(cycle_data['I'])
                
                if len(time_min) > 0 and len(current_A) > 0:
                    # Normalize time to start from 0 and convert to seconds
                    time_min_normalized = time_min - time_min[0]
                    time_s = time_min_normalized * 60
                    
                    # Create DataFrame
                    df = pd.DataFrame({
                        "Time(s)": time_s,
                        "Time(min)": time_min_normalized,
                        "Current(A)": current_A
                    })
                    
                    all_cycles_data[cycle_num] = df
                    
                    # Extract capacity and CE values
                    if cycle_num < len(qd_values):
                        cycle_capacities[cycle_num] = qd_values[cycle_num] / 1000
                    
                    if cycle_num < len(ce_values):
                        cycle_ce_values[cycle_num] = ce_values[cycle_num]
                    
                    log(f"  Extracted cycle {cycle_num}: {len(df)} points", text_widget)
            
            except (KeyError, ValueError, TypeError) as e:
                log(f"  Error processing cycle {cycle_str}: {str(e)}", text_widget)
                continue
        
        log(f"Successfully extracted {len(all_cycles_data)} cycles from {cell_key}", text_widget)
        
    except Exception as e:
        log(f"Error processing MIT CV JSON data: {str(e)}", text_widget)
        return {}, {}, {}
    
    return all_cycles_data, cycle_capacities, cycle_ce_values

def detect_mit_cv_format(json_data):
    """Detect if JSON data is in MIT CV format"""
    if not isinstance(json_data, dict):
        return False
    
    for key, value in json_data.items():
        if key.startswith('b3c') and isinstance(value, dict):
            if 'cv_cycles' in value and 'summary' in value:
                return True
    
    return False

def get_available_battery_cells(json_data):
    """Get list of available battery cells from MIT CV data"""
    if not isinstance(json_data, dict):
        return []
    
    return [key for key in json_data.keys() if key.startswith('b3c')]

class BatteryAnalysisApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Battery Analysis Tool")
        self.root.geometry("1400x900")

        # Data storage
        self.all_cycles_data = {}
        self.cycle_capacities = {}
        self.cycle_ce_values = {}
        self.didt_cycles_data = {}
        self.sample_name = ""
        self.current_json_data = None
        self.available_cells = []

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="File Selection")
        file_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(file_frame, text="Load File", command=self.load_file).pack(side=tk.LEFT)

        # Cell selection frame (initially hidden)
        self.cell_frame = ttk.LabelFrame(main_frame, text="Battery Cell Selection")
        self.cell_var = tk.StringVar()
        self.cell_combo = ttk.Combobox(self.cell_frame, textvariable=self.cell_var, state="readonly")
        self.cell_combo.pack(side=tk.LEFT, padx=5)
        self.cell_combo.bind('<<ComboboxSelected>>', self.on_cell_changed)
        ttk.Button(self.cell_frame, text="Load Selected Cell", command=self.load_selected_cell).pack(side=tk.LEFT, padx=5)

        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Processing Log")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Create text widget with scrollbar
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(text_frame, wrap=tk.WORD, height=15)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Welcome message
        self.log("Battery Analysis Tool - Ready to load data")

    def log(self, message):
        """Log message to the text widget"""
        log(message, self.log_text)

    def load_file(self):
        """Load file using file dialog"""
        file_path = filedialog.askopenfilename(
            title="Select Data File",
            filetypes=(
                ("JSON Files", "*.json"),
                ("CSV Files", "*.csv"),
                ("All Files", "*.*"),
            ),
        )
        
        if file_path:
            self.log(f"Selected file: {file_path}")
            self.process_selected_file(file_path)

    def process_selected_file(self, file_path):
        """Process a selected file"""
        try:
            self.log(f"Processing file: {file_path}")
            
            # Set sample name from filename
            base_name = os.path.basename(file_path)
            self.sample_name = os.path.splitext(base_name)[0]
            
            # Check file extension
            ext = os.path.splitext(file_path)[1].lower()
            
            if ext == ".json":
                self.log("Detected JSON file, loading data...")
                with open(file_path, "r") as f:
                    data = json.load(f)
                self.log(f"Loaded JSON file with {len(data)} elements")
                
                # Detect data format and extract accordingly
                if detect_mit_cv_format(data):
                    self.log("Detected MIT CV data format")
                    self.current_json_data = data
                    
                    # Get available battery cells
                    self.available_cells = get_available_battery_cells(data)
                    self.log(f"Found battery cells: {', '.join(self.available_cells)}")
                    
                    # Show cell selection UI
                    self.cell_frame.pack(fill=tk.X, pady=(5, 0))
                    self.cell_combo['values'] = self.available_cells
                    if self.available_cells:
                        self.cell_combo.set(self.available_cells[0])
                    
                    # Load first cell by default
                    self.load_selected_cell()
                    return
                else:
                    self.log("Unsupported JSON format")
                    return
                    
            else:
                self.log(f"Error: Unsupported file extension: {ext}")
                return
                
        except Exception as e:
            self.log(f"Error processing file: {str(e)}")
            self.log(traceback.format_exc())

    def on_cell_changed(self, event=None):
        """Handle battery cell selection change"""
        pass

    def load_selected_cell(self):
        """Load data for selected battery cell"""
        if not self.current_json_data or not self.cell_var.get():
            return
            
        selected_cell = self.cell_var.get()
        self.log(f"Loading data for battery cell: {selected_cell}")
        
        try:
            # Extract data for selected cell
            all_cycles_data, cycle_capacities, cycle_ce_values = (
                extract_cycles_from_mit_cv_json(self.current_json_data, 
                                               self.log_text, selected_cell)
            )
            
            if all_cycles_data:
                self.all_cycles_data = all_cycles_data
                self.cycle_capacities = cycle_capacities
                self.cycle_ce_values = cycle_ce_values
                
                # Calculate dI/dt for all cycles
                self.didt_cycles_data = self.calculate_didt_for_all_cycles()
                
                self.log(f"Loaded {len(self.all_cycles_data)} cycles for {selected_cell}")
                
                # Enable visualization
                self.visualize_interactive()
            else:
                self.log(f"No valid data found for {selected_cell}")
                
        except Exception as e:
            self.log(f"Error loading cell data: {str(e)}")

    def calculate_didt_for_all_cycles(self):
        """Calculate dI/dt for all loaded cycles"""
        didt_cycles_data = {}
        
        for cycle_num, cycle_df in self.all_cycles_data.items():
            try:
                time_s = cycle_df['Time(s)'].values
                current_A = cycle_df['Current(A)'].values
                
                if len(time_s) > 1 and len(current_A) > 1:
                    # Calculate dI/dt using vectorized method
                    didt_values, didt_times = compute_dIdt_vectorized(time_s, current_A)
                    
                    if didt_values is not None and didt_times is not None:
                        # Create dI/dt DataFrame
                        didt_df = pd.DataFrame({
                            'Time(s)': didt_times,
                            'dI/dt_raw(A/s)': didt_values
                        })
                        
                        # Add smoothed dI/dt
                        didt_df['dI/dt_smooth(A/s)'] = (
                            didt_df['dI/dt_raw(A/s)'].rolling(window=5, center=True).mean()
                        )
                        didt_df['dI/dt_smooth(A/s)'] = didt_df['dI/dt_smooth(A/s)'].fillna(
                            didt_df['dI/dt_raw(A/s)']
                        )
                        
                        didt_cycles_data[cycle_num] = didt_df
                    
            except Exception as e:
                self.log(f"Error calculating dI/dt for cycle {cycle_num}: {str(e)}")
                continue
        
        return didt_cycles_data

    def visualize_interactive(self):
        """Launch interactive visualization window"""
        self.log("Launching interactive visualization...")

        try:
            def run_visualization():
                try:
                    interactive_cycle_viewer(
                        self.all_cycles_data,
                        self.didt_cycles_data,
                        self.cycle_capacities,
                        self.sample_name,
                        cycle_ce_values=self.cycle_ce_values
                    )
                except Exception as e:
                    self.log(f"Error in visualization: {str(e)}")

            threading.Thread(target=run_visualization, daemon=True).start()

        except Exception as e:
            self.log(f"Error launching visualization: {str(e)}")

def interactive_cycle_viewer(all_cycles_data, didt_cycles_data, cycle_capacities, 
                           sample_name, cycle_ce_values=None, knee_points=None):
    """Interactive visualization for cycle data with improved structure"""
    # Create a new window for the interactive viewer
    viewer = tk.Toplevel()
    viewer.title(f"Interactive Cycle Viewer - {sample_name}")
    viewer.geometry("1200x900")

    # Create a PlotManager instance
    plot_manager = PlotManager()

    # Get available cycles
    available_cycles = sorted(list(all_cycles_data.keys()))
    if not available_cycles:
        messagebox.showerror("Error", "No cycle data available for visualization")
        viewer.destroy()
        return

    min_cycle = min(available_cycles)
    max_cycle = max(available_cycles)

    # Create main frame
    main_frame = ttk.Frame(viewer)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Create matplotlib figure with 2x2 subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle(f"Battery Analysis - {sample_name}", fontsize=16)

    # Store axes in a dictionary for easier access
    axes = {
        "current": ax1,
        "didt": ax2, 
        "capacity": ax3,
        "knee": ax4
    }

    # Create canvas
    canvas = FigureCanvasTkAgg(fig, main_frame)
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # Control frame
    control_frame = ttk.Frame(main_frame)
    control_frame.pack(fill=tk.X, pady=(10, 0))

    # Cycle selection
    cycle_frame = ttk.LabelFrame(control_frame, text="Cycle Selection")
    cycle_frame.pack(side=tk.LEFT, fill=tk.Y)

    cycle_var = tk.IntVar(value=min_cycle)
    cycle_scale = ttk.Scale(cycle_frame, from_=min_cycle, to=max_cycle, 
                           variable=cycle_var, orient=tk.HORIZONTAL, length=200)
    cycle_scale.pack(padx=5, pady=5)

    cycle_label = ttk.Label(cycle_frame, text=f"Cycle: {min_cycle}")
    cycle_label.pack()

    # Information display
    info_frame = ttk.LabelFrame(control_frame, text="Information")
    info_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=(10, 0))

    cycle_info_var = tk.StringVar()
    capacity_info_var = tk.StringVar()
    ce_info_var = tk.StringVar()

    ttk.Label(info_frame, textvariable=cycle_info_var).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(info_frame, textvariable=capacity_info_var).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(info_frame, textvariable=ce_info_var).pack(anchor=tk.W, padx=5, pady=2)

    def update_plots():
        """Update all plots showing all cycles with selected cycle highlighted"""
        cycle = cycle_var.get()
        
        # Clear all axes
        for ax_name in axes:
            axes[ax_name].clear()

        # Reset plot titles and labels
        axes["current"].set_title("Current vs Time - All Cycles")
        axes["current"].set_xlabel("Time (s)")
        axes["current"].set_ylabel("Current (A)")

        axes["didt"].set_title("dI/dt vs Time - All Cycles")
        axes["didt"].set_xlabel("Time (s)")
        axes["didt"].set_ylabel("dI/dt (A/s)")

        axes["capacity"].set_title("Capacity vs Cycle")
        axes["capacity"].set_xlabel("Cycle Number")
        axes["capacity"].set_ylabel("Capacity (Ah)")

        axes["knee"].set_title("Cycle Analysis")
        axes["knee"].set_xlabel("Cycle Number")
        axes["knee"].set_ylabel("Value")

        # Plot current data for all cycles with gradient colors
        n_cycles = len(available_cycles)
        color_map = cm.get_cmap("viridis", n_cycles)
        colors = [color_map(i) for i in range(n_cycles)]

        current_time_min = None
        current_time_max = None
        for i, c in enumerate(available_cycles):
            if c in all_cycles_data:
                color = colors[i]
                plot_manager.plot_cycle_current(
                    axes["current"], all_cycles_data[c], c, color=color
                )
                # Record time range
                tmin = all_cycles_data[c]["Time(s)"].min()
                tmax = all_cycles_data[c]["Time(s)"].max()
                if current_time_min is None or tmin < current_time_min:
                    current_time_min = tmin
                if current_time_max is None or tmax > current_time_max:
                    current_time_max = tmax

        # Highlight the selected cycle with thicker black line
        if cycle in all_cycles_data:
            plot_manager.plot_cycle_current(
                axes["current"], all_cycles_data[cycle], cycle, color="black"
            )

        # Plot dI/dt data for all cycles
        all_didt_min = 0
        all_didt_max = 0

        for i, c in enumerate(available_cycles):
            if (c in didt_cycles_data and didt_cycles_data[c] is not None 
                and len(didt_cycles_data[c]) > 0):
                color = colors[i]
                line = plot_manager.plot_cycle_didt(
                    axes["didt"], didt_cycles_data[c], c, color=color,
                    linestyle="-", alpha=0.7, zorder=5
                )

                # Collect min/max values for y-axis range
                if line is not None:
                    if "dI/dt_smooth(A/s)" in didt_cycles_data[c].columns:
                        didt_min = didt_cycles_data[c]["dI/dt_smooth(A/s)"].min()
                        didt_max = didt_cycles_data[c]["dI/dt_smooth(A/s)"].max()
                        all_didt_min = min(all_didt_min, didt_min)
                        all_didt_max = max(all_didt_max, didt_max)

        # Highlight the selected cycle with thicker black line
        if (cycle in didt_cycles_data and didt_cycles_data[cycle] is not None 
            and len(didt_cycles_data[cycle]) > 0):
            plot_manager.plot_cycle_didt(
                axes["didt"], didt_cycles_data[cycle], cycle, color="black",
                linestyle="-", alpha=1.0, zorder=10
            )

        # Set consistent x-axis range for dI/dt plot
        if current_time_min is not None and current_time_max is not None:
            axes["didt"].set_xlim(current_time_min, current_time_max)

        # Set y-axis range for dI/dt plot
        if all_didt_min != 0 or all_didt_max != 0:
            y_margin = max(abs(all_didt_max - all_didt_min) * 0.1, 0.001)
            axes["didt"].set_ylim(all_didt_min - y_margin, all_didt_max + y_margin)
        else:
            axes["didt"].set_ylim(-0.05, 0.01)

        # Plot capacity data (左下角图)
        plot_manager.plot_capacities(
            axes["capacity"], cycle_capacities, knee_points, point_colors=colors
        )

        # Mark selected cycle in capacity plot
        if cycle in cycle_capacities:
            axes["capacity"].plot(
                [cycle], [cycle_capacities[cycle]], "go", markersize=10
            )

        # Plot cycle analysis data (右下角图)
        cycles = sorted(list(cycle_capacities.keys()))
        capacities = [cycle_capacities[c] for c in cycles]

        # Plot capacity derivative (rate of capacity change)
        if len(cycles) > 3:
            try:
                capacity_array = np.array(capacities)
                cycle_array = np.array(cycles)
                
                # Calculate capacity derivative using gradient
                capacity_derivative = np.gradient(capacity_array, cycle_array)
                
                # Plot capacity derivative
                axes["knee"].plot(cycles, capacity_derivative, 'b-', linewidth=2, 
                                 label='dCapacity/dCycle', alpha=0.8)
                
                # Highlight selected cycle
                if cycle in cycle_capacities:
                    cycle_idx = cycles.index(cycle) if cycle in cycles else None
                    if cycle_idx is not None:
                        axes["knee"].plot([cycle], [capacity_derivative[cycle_idx]], 
                                        'ro', markersize=8, markerfacecolor='red',
                                        markeredgecolor='black', markeredgewidth=1)

                # Mark knee points if available
                if knee_points:
                    knee_y_values = []
                    for knee in knee_points:
                        if knee in cycles:
                            knee_idx = cycles.index(knee)
                            knee_y_values.append(capacity_derivative[knee_idx])
                        else:
                            knee_y_values.append(0)
                    
                    axes["knee"].plot(
                        knee_points,
                        knee_y_values,
                        "rx",
                        markersize=10,
                        markeredgewidth=2,
                        label="Knee Points",
                    )

                axes["knee"].set_xlabel("Cycle Number")
                axes["knee"].set_ylabel("dCapacity/dCycle (Ah/cycle)")
                axes["knee"].set_title("Capacity Derivative Analysis")
                axes["knee"].legend(loc="best")
                
            except Exception as e:
                print(f"Error plotting capacity derivative: {str(e)}")
                # Fallback to CE plot if capacity derivative fails
                if cycle_ce_values and len(cycle_ce_values) > 0:
                    ce_cycles = sorted(list(cycle_ce_values.keys()))
                    ce_values = [cycle_ce_values[c] for c in ce_cycles]

                    # Plot CE data
                    axes["knee"].plot(ce_cycles, ce_values, 'g-o', markersize=4, 
                                     linewidth=1.5, label='Coulombic Efficiency')
                    
                    # Highlight selected cycle
                    if cycle in cycle_ce_values:
                        axes["knee"].plot([cycle], [cycle_ce_values[cycle]], 
                                        'ro', markersize=8)

                    axes["knee"].set_xlabel("Cycle Number")
                    axes["knee"].set_ylabel("Coulombic Efficiency")
                    axes["knee"].set_title("Coulombic Efficiency vs Cycle")
                    axes["knee"].legend(loc="best")
        else:
            # If not enough cycles for derivative, plot CE data
            if cycle_ce_values and len(cycle_ce_values) > 0:
                ce_cycles = sorted(list(cycle_ce_values.keys()))
                ce_values = [cycle_ce_values[c] for c in ce_cycles]

                # Plot CE data
                axes["knee"].plot(ce_cycles, ce_values, 'g-o', markersize=4, 
                                 linewidth=1.5, label='Coulombic Efficiency')
                
                # Highlight selected cycle
                if cycle in cycle_ce_values:
                    axes["knee"].plot([cycle], [cycle_ce_values[cycle]], 
                                    'ro', markersize=8)

                axes["knee"].set_xlabel("Cycle Number")
                axes["knee"].set_ylabel("Coulombic Efficiency")
                axes["knee"].set_title("Coulombic Efficiency vs Cycle")
                axes["knee"].legend(loc="best")
            else:
                # If no CE data, show a placeholder
                axes["knee"].text(0.5, 0.5, 'No Analysis Data Available', 
                                 transform=axes["knee"].transAxes, 
                                 ha='center', va='center', fontsize=12)
                axes["knee"].set_title("Cycle Analysis")

        # Add grid to all plots
        for ax in axes.values():
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        canvas.draw()

    def update_info():
        """Update information display"""
        cycle = cycle_var.get()
        cycle_label.config(text=f"Cycle: {cycle}")
        
        cycle_info = f"Cycle: {cycle}/{max_cycle}"
        capacity = cycle_capacities.get(cycle, 0)
        capacity_info = f"Capacity: {capacity:.4f} Ah"
        
        if cycle_ce_values and cycle in cycle_ce_values:
            ce = cycle_ce_values[cycle]
            ce_info = f"CE: {ce:.6f}"
        else:
            ce_info = "CE: N/A"

        cycle_info_var.set(cycle_info)
        capacity_info_var.set(capacity_info)
        ce_info_var.set(ce_info)

    # Bind scale change event
    def on_scale_change(*args):
        update_plots()
        update_info()

    cycle_var.trace_add("write", on_scale_change)

    # Initial plot
    update_plots()
    update_info()

    # Handle window close event
    def on_close():
        plt.close(fig)
        viewer.destroy()

    viewer.protocol("WM_DELETE_WINDOW", on_close)

    # Start the event loop
    viewer.mainloop()

def main():
    """Main entry point for the application"""
    try:
        root = tk.Tk()
        root.title("Battery Analysis Tool")

        # Create and run application
        app = BatteryAnalysisApp(root)

        # Center window on screen
        window_width = 1000
        window_height = 800
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = int((screen_width - window_width) / 2)
        y = int((screen_height - window_height) / 2)
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Start the application
        root.mainloop()

    except Exception as e:
        print(f"Error starting application: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()

